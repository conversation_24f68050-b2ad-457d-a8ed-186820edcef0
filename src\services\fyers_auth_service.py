"""
Fyers Authentication Service.
Provides authentication and data fetching capabilities using the auth folder code.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
import sys

# Add auth folder to path
auth_path = Path(__file__).parent
#sys.path.insert(0, str(auth_path))

from src.auth.fyers_client import FyersClient, MarketData, OHLCData
from src.auth.fyers_config import FyersConfig
from src.core.config import settings

logger = logging.getLogger(__name__)


class FyersAuthService:
    """
    Service for Fyers API authentication and data operations.
    Uses the auth folder code for all Fyers interactions.
    """
    
    def __init__(self):
        """Initialize the Fyers authentication service."""
        self.fyers_client = None
        self.is_initialized = False
        
    def initialize(self) -> bool:
        """
        Initialize Fyers authentication.
        
        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Initializing Fyers authentication service...")
            
            # Initialize Fyers client with auth folder code
            self.fyers_client = FyersClient()
            
            # Authenticate
            if self.fyers_client.authenticate():
                self.is_initialized = True
                logger.info("Fyers authentication service initialized successfully")
                return True
            else:
                logger.error("Failed to authenticate with Fyers API")
                return False
                
        except Exception as e:
            logger.error(f"Error initializing Fyers authentication service: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """
        Check if service is authenticated.
        
        Returns:
            True if authenticated, False otherwise
        """
        return self.is_initialized and self.fyers_client and self.fyers_client.is_authenticated()
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, MarketData]:
        """
        Get market quotes for symbols.
        
        Args:
            symbols: List of symbol strings
            
        Returns:
            Dictionary mapping symbol to MarketData
        """
        if not self.is_authenticated():
            logger.error("Fyers service not authenticated")
            return {}
            
        return self.fyers_client.get_quotes(symbols)
    
    def get_single_quote(self, symbol: str) -> Optional[MarketData]:
        """
        Get market quote for a single symbol.
        
        Args:
            symbol: Symbol string
            
        Returns:
            MarketData object or None
        """
        if not self.is_authenticated():
            logger.error("Fyers service not authenticated")
            return None
            
        return self.fyers_client.get_single_quote(symbol)
    
    def get_historical_data(self, symbol: str, interval: int, days_to_fetch: int) -> List[OHLCData]:
        """
        Get historical OHLC data for a symbol.
        
        Args:
            symbol: Symbol string
            interval: Timeframe interval
            days_to_fetch: Number of days to fetch
            
        Returns:
            List of OHLCData objects
        """
        if not self.is_authenticated():
            logger.error("Fyers service not authenticated")
            return []
            
        return self.fyers_client.get_historical_data(symbol, interval, days_to_fetch)
    
    def fetch_historical_data_chunked(
        self, 
        symbol: str, 
        start_date: datetime, 
        end_date: datetime,
        interval: int = 1
    ) -> List[Dict[str, Any]]:
        """
        Fetch historical data in chunks for large date ranges.
        
        Args:
            symbol: Symbol to fetch data for
            start_date: Start date
            end_date: End date
            interval: Timeframe interval (default: 1 minute)
            
        Returns:
            List of OHLCV data dictionaries
        """
        if not self.is_authenticated():
            logger.error("Fyers service not authenticated")
            return []
        
        try:
            # Calculate total days
            total_days = (end_date - start_date).days
            
            # Fetch data in chunks to avoid API limits
            chunk_size_days = 90  # 90-day chunks
            all_data = []
            
            current_start = start_date
            while current_start < end_date:
                chunk_end = min(current_start + timedelta(days=chunk_size_days), end_date)
                chunk_days = (chunk_end - current_start).days
                
                logger.info(f"Fetching {symbol} data from {current_start.date()} to {chunk_end.date()}")
                
                # Get historical data for this chunk
                ohlc_data = self.fyers_client.get_historical_data(symbol, interval, chunk_days)
                
                # Convert to dictionary format
                for ohlc in ohlc_data:
                    data_dict = {
                        'timestamp': ohlc.timestamp,  # Already formatted as string by FyersClient
                        'open': ohlc.open,
                        'high': ohlc.high,
                        'low': ohlc.low,
                        'close': ohlc.close,
                        'volume': ohlc.volume
                    }
                    all_data.append(data_dict)
                
                current_start = chunk_end + timedelta(days=1)
                
                # Rate limiting
                time.sleep(2)
            
            logger.info(f"Fetched {len(all_data)} records for {symbol}")
            return all_data
            
        except Exception as e:
            logger.error(f"Error fetching historical data for {symbol}: {e}")
            return []
    
    def convert_to_fyers_symbol(self, symbol: str, market_type: str = "EQUITY") -> str:
        """
        Convert symbol to Fyers format.
        
        Args:
            symbol: Original symbol
            market_type: Market type (EQUITY, INDEX, etc.)
            
        Returns:
            Fyers formatted symbol
        """
        # Basic conversion logic - can be enhanced based on requirements
        if market_type == "EQUITY":
            return f"NSE:{symbol}-EQ"
        elif market_type == "INDEX":
            return f"NSE:{symbol}-INDEX"
        elif market_type == "FUTURES":
            return f"NSE:{symbol}-FUT"
        elif market_type == "OPTIONS":
            return f"NSE:{symbol}-OPT"
        else:
            return f"NSE:{symbol}-EQ"  # Default to equity
    
    def cleanup(self):
        """Cleanup resources."""
        if self.fyers_client:
            # Any cleanup needed for fyers_client
            pass
        self.is_initialized = False
        logger.info("Fyers authentication service cleaned up")
