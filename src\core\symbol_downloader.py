import os
import sys
import requests
import yaml
import logging
import pandas as pd
import io
from datetime import datetime
from urllib.parse import urlparse
from typing import List, Dict, Tuple, Optional
from pathlib import Path

# Add src to path for imports
project_root = Path(__file__).parent

logger = logging.getLogger(__name__)

def get_config():
    """Loads the configuration from config.yaml."""
    config_path = project_root / "config.yaml"
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)

def parse_nse_cm_symbols(csv_content: str) -> Dict[str, List[Dict]]:
    """
    Parse NSE_CM.csv content to extract equity and index symbols.

    Args:
        csv_content: Raw CSV content as string

    Returns:
        Dictionary with 'equities' and 'indices' lists
    """
    try:
        df = pd.read_csv(io.StringIO(csv_content))

        equities = []
        indices = []

        for _, row in df.iterrows():
            try:
                symbol_name = row.get('Fytoken', '')
                trading_symbol = row.get('Symbol', '')
                company_name = row.get('CompanyName', trading_symbol)
                segment = row.get('Segment', '')

                if not trading_symbol:
                    continue

                symbol_data = {
                    'symbol': trading_symbol,
                    'name': company_name,
                    'fyers_symbol': symbol_name,
                    'segment': segment
                }

                # Determine if it's an index or equity
                if 'INDEX' in segment.upper() or trading_symbol.endswith('INDEX'):
                    indices.append(symbol_data)
                else:
                    equities.append(symbol_data)

            except Exception as e:
                logger.warning(f"Error parsing NSE_CM row: {e}")
                continue

        logger.info(f"Parsed NSE_CM: {len(equities)} equities, {len(indices)} indices")
        return {'equities': equities, 'indices': indices}

    except Exception as e:
        logger.error(f"Error parsing NSE_CM.csv: {e}")
        return {'equities': [], 'indices': []}

def parse_nse_fo_symbols(csv_content: str) -> Dict[str, List[Dict]]:
    """
    Parse NSE_FO.csv content to extract futures and options symbols.

    Args:
        csv_content: Raw CSV content as string

    Returns:
        Dictionary with 'futures' and 'options' lists
    """
    try:
        df = pd.read_csv(io.StringIO(csv_content))

        futures = []
        options = []

        for _, row in df.iterrows():
            try:
                symbol_name = row.get('Fytoken', '')
                trading_symbol = row.get('Symbol', '')
                company_name = row.get('CompanyName', trading_symbol)
                expiry = row.get('Expiry', '')
                strike_price = row.get('StrikePrice', '')
                option_type = row.get('OptionType', '')

                if not trading_symbol:
                    continue

                symbol_data = {
                    'symbol': trading_symbol,
                    'name': company_name,
                    'fyers_symbol': symbol_name,
                    'expiry': expiry,
                    'strike_price': strike_price,
                    'option_type': option_type
                }

                # Determine if it's futures or options
                if option_type and option_type.upper() in ['CE', 'PE', 'CALL', 'PUT']:
                    options.append(symbol_data)
                else:
                    futures.append(symbol_data)

            except Exception as e:
                logger.warning(f"Error parsing NSE_FO row: {e}")
                continue

        logger.info(f"Parsed NSE_FO: {len(futures)} futures, {len(options)} options")
        return {'futures': futures, 'options': options}

    except Exception as e:
        logger.error(f"Error parsing NSE_FO.csv: {e}")
        return {'futures': [], 'options': []}

def download_file(url: str, destination: str) -> bool:
    """
    Downloads a file from a URL to a destination.

    Args:
        url: URL to download from
        destination: Local file path to save to

    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Downloading from {url}...")
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        with open(destination, 'wb') as f:
            f.write(response.content)

        logger.info(f"Successfully downloaded to {destination}")
        return True

    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download {url}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error downloading {url}: {e}")
        return False

def determine_csv_filename(url: str) -> str:
    """
    Determine the local CSV filename based on the URL.

    Args:
        url: URL to analyze

    Returns:
        Local CSV filename
    """
    # Extract filename from URL
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)

    # Map known patterns to standard filenames
    if 'NSE_CM' in filename or 'NSE_CM' in url:
        return 'NSE_CM.csv'
    elif 'NSE_FO' in filename or 'NSE_FO' in url:
        return 'NSE_FO.csv'
    elif 'MCX' in filename or 'MCX' in url:
        return 'MCX.csv'
    elif filename.endswith('.csv'):
        return filename
    else:
        # Default fallback - use the last part of the URL path
        return f"{filename}.csv" if not filename.endswith('.csv') else filename

def backup_existing_file(local_file_path: str, mastersymbol_dir: str) -> bool:
    """
    Backup an existing file to the mastersymbol directory.

    Args:
        local_file_path: Path to the file to backup
        mastersymbol_dir: Directory to store backups

    Returns:
        True if backup was successful or file didn't exist, False otherwise
    """
    try:
        if os.path.exists(local_file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_prefix = os.path.splitext(os.path.basename(local_file_path))[0]
            backup_path = os.path.join(mastersymbol_dir, f"{file_prefix}_{timestamp}.csv")

            os.rename(local_file_path, backup_path)
            logger.info(f"Backed up existing file to {backup_path}")
            print(f"Backed up existing file to {backup_path}")

        return True

    except Exception as e:
        logger.error(f"Failed to backup {local_file_path}: {e}")
        return False

def download_symbols_from_urls(urls: List[str]) -> Dict[str, bool]:
    """
    Download symbol files from multiple URLs.

    Args:
        urls: List of URLs to download from

    Returns:
        Dictionary mapping local filename to success status
    """
    results = {}
    mastersymbol_dir = 'mastersymbol'

    # Create or clear the mastersymbol directory
    try:
        if not os.path.exists(mastersymbol_dir):
            os.makedirs(mastersymbol_dir)
            logger.info(f"Created backup directory: {mastersymbol_dir}")
        else:
            # Clean up old backups (keep only recent ones)
            cleanup_old_backups(mastersymbol_dir)
    except Exception as e:
        logger.error(f"Failed to setup backup directory: {e}")
        return results

    # Process each URL
    for url in urls:
        try:
            # Determine local filename
            local_file_path = determine_csv_filename(url)

            logger.info(f"Processing URL: {url} -> {local_file_path}")

            # Backup existing file
            if not backup_existing_file(local_file_path, mastersymbol_dir):
                logger.warning(f"Backup failed for {local_file_path}, continuing anyway")

            # Download the new file
            success = download_file(url, local_file_path)
            results[local_file_path] = success

            if success:
                print(f"Downloaded and saved: {local_file_path}")
            else:
                print(f"Failed to download: {local_file_path}")

        except Exception as e:
            logger.error(f"Error processing URL {url}: {e}")
            results[url] = False

    return results

def cleanup_old_backups(mastersymbol_dir: str, keep_count: int = 5):
    """
    Clean up old backup files, keeping only the most recent ones.

    Args:
        mastersymbol_dir: Directory containing backups
        keep_count: Number of recent backups to keep per file type
    """
    try:
        if not os.path.exists(mastersymbol_dir):
            return

        # Group files by prefix
        file_groups = {}
        for filename in os.listdir(mastersymbol_dir):
            if filename.endswith('.csv'):
                # Extract prefix (everything before the timestamp)
                parts = filename.split('_')
                if len(parts) >= 2:
                    prefix = '_'.join(parts[:-2])  # Everything except timestamp parts
                    if prefix not in file_groups:
                        file_groups[prefix] = []
                    file_groups[prefix].append(filename)

        # Clean up each group
        for prefix, files in file_groups.items():
            if len(files) > keep_count:
                # Sort by modification time (newest first)
                files_with_time = [(f, os.path.getmtime(os.path.join(mastersymbol_dir, f))) for f in files]
                files_with_time.sort(key=lambda x: x[1], reverse=True)

                # Remove old files
                for filename, _ in files_with_time[keep_count:]:
                    file_path = os.path.join(mastersymbol_dir, filename)
                    os.remove(file_path)
                    logger.debug(f"Removed old backup: {filename}")

    except Exception as e:
        logger.error(f"Error cleaning up old backups: {e}")

def download_and_parse_symbols() -> Dict[str, Dict]:
    """
    Download symbol files and parse them into structured data.

    Returns:
        Dictionary with parsed symbol data for all market types
    """
    try:
        logger.info("🔄 Starting automatic symbol download and parsing...")

        config = get_config()
        remote_urls = config['general']['fyers_api_url']

        # Ensure remote_urls is a list
        if isinstance(remote_urls, str):
            remote_urls = [remote_urls]

        if not remote_urls:
            logger.error("No URLs configured in fyers_api_url")
            return {}

        all_symbols = {
            'equities': [],
            'indices': [],
            'futures': [],
            'options': []
        }

        for url in remote_urls:
            try:
                logger.info(f"📥 Downloading from {url}")
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                csv_content = response.text

                # Parse based on URL type
                if 'NSE_CM' in url:
                    parsed_data = parse_nse_cm_symbols(csv_content)
                    all_symbols['equities'].extend(parsed_data['equities'])
                    all_symbols['indices'].extend(parsed_data['indices'])

                elif 'NSE_FO' in url:
                    parsed_data = parse_nse_fo_symbols(csv_content)
                    all_symbols['futures'].extend(parsed_data['futures'])
                    all_symbols['options'].extend(parsed_data['options'])

                logger.info(f"✅ Successfully processed {url}")

            except Exception as e:
                logger.error(f"❌ Failed to process {url}: {e}")
                continue

        # Log summary
        total_symbols = sum(len(symbols) for symbols in all_symbols.values())
        logger.info(f"📊 Symbol parsing complete:")
        logger.info(f"   Equities: {len(all_symbols['equities'])}")
        logger.info(f"   Indices: {len(all_symbols['indices'])}")
        logger.info(f"   Futures: {len(all_symbols['futures'])}")
        logger.info(f"   Options: {len(all_symbols['options'])}")
        logger.info(f"   Total: {total_symbols}")

        return all_symbols

    except Exception as e:
        logger.error(f"Symbol download and parsing failed: {e}")
        return {}

def integrate_with_symbol_service() -> bool:
    """
    Download symbols and integrate with the symbol service.

    Returns:
        True if successful, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from src.services.symbol_service import SymbolService
        from src.database.connection import get_db

        logger.info("🔗 Integrating symbol download with symbol service...")

        # Download and parse symbols
        symbols_data = download_and_parse_symbols()

        if not symbols_data:
            logger.error("No symbols data retrieved")
            return False

        # Initialize symbol service
        db = next(get_db())
        symbol_service = SymbolService(db)

        # Process symbols through symbol service
        results = symbol_service.fetch_nse_symbols(force_refresh=True)

        logger.info(f"✅ Symbol integration completed: {results}")
        return True

    except Exception as e:
        logger.error(f"Symbol integration failed: {e}")
        return False

def main():
    """Main function to download and backup the symbol files."""
    try:
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        logger.info("Starting symbol file download process")

        config = get_config()
        remote_urls = config['general']['fyers_api_url']

        # Ensure remote_urls is a list
        if isinstance(remote_urls, str):
            remote_urls = [remote_urls]

        if not remote_urls:
            logger.error("No URLs configured in fyers_api_url")
            print("Error: No URLs configured in config.yaml")
            return False

        logger.info(f"Found {len(remote_urls)} URLs to download")
        print(f"Downloading symbol files from {len(remote_urls)} sources...")

        # Download all files
        results = download_symbols_from_urls(remote_urls)

        # Report results
        successful_downloads = sum(1 for success in results.values() if success)
        total_downloads = len(results)

        logger.info(f"Download complete: {successful_downloads}/{total_downloads} successful")
        print(f"\nDownload Summary:")
        print(f"  Successful: {successful_downloads}/{total_downloads}")

        for filename, success in results.items():
            status = "SUCCESS" if success else "FAILED"
            print(f"  {status}: {filename}")

        return successful_downloads == total_downloads

    except Exception as e:
        logger.error(f"Symbol download failed: {e}")
        print(f"Error: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)