"""
Robust Data Storage Service - Database Models.
Optimized for TimescaleDB with separate tables for different market segments.
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, Enum, Index, ForeignKey, Date, DECIMAL, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func, text
from datetime import datetime, date
import enum

from src.database.connection import Base


class MarketType(enum.Enum):
    """Market type enumeration."""
    EQUITY = "EQUITY"
    INDEX = "INDEX"
    FUTURES = "FUTURES"
    OPTIONS = "OPTIONS"
    COMMODITY = "COMMODITY"
    CURRENCY = "CURRENCY"


class OptionType(enum.Enum):
    """Option type enumeration."""
    CE = "CE"  # Call European
    PE = "PE"  # Put European


class Symbol(Base):
    """Symbol master table for storing symbol metadata."""

    __tablename__ = "symbols"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(100), unique=True, index=True, nullable=False)
    name = Column(String(200), nullable=False)
    market_type = Column(Enum(MarketType), nullable=False, default=MarketType.EQUITY)
    exchange = Column(String(10), nullable=False, default="NSE")
    sector = Column(String(100))
    industry = Column(String(100))
    lot_size = Column(Integer, default=1)
    tick_size = Column(DECIMAL(10, 4), default=0.05)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Symbol(symbol='{self.symbol}', name='{self.name}', market_type='{self.market_type}')>"


class EquityOHLCV(Base):
    """Equity OHLCV data model optimized for TimescaleDB - matches reference stock_ohlcv structure."""

    __tablename__ = "equity_ohlcv"

    symbol = Column(Text, nullable=False, primary_key=True, index=True)
    exchange = Column(Text, nullable=False, default='NSE', primary_key=True)
    interval = Column('interval', Text, nullable=False, default='1m', primary_key=True)
    datetime = Column(DateTime(timezone=False), nullable=False, primary_key=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    created_at = Column(DateTime(timezone=False), server_default=text("(now() AT TIME ZONE 'UTC')"))

    # Indexes for performance
    __table_args__ = (
        Index('idx_equity_symbol_datetime', 'symbol', 'datetime'),
        Index('idx_equity_datetime_desc', 'datetime', postgresql_using='btree'),
    )

    def __repr__(self):
        return f"<EquityOHLCV(symbol='{self.symbol}', datetime='{self.datetime}', close={self.close})>"


class IndexOHLCV(Base):
    """Index OHLCV data model optimized for TimescaleDB - matches reference stock_ohlcv structure."""

    __tablename__ = "index_ohlcv"

    symbol = Column(Text, nullable=False, primary_key=True, index=True)
    exchange = Column(Text, nullable=False, default='NSE', primary_key=True)
    interval = Column('interval', Text, nullable=False, default='1m', primary_key=True)
    datetime = Column(DateTime(timezone=False), nullable=False, primary_key=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    created_at = Column(DateTime(timezone=False), server_default=text("(now() AT TIME ZONE 'UTC')"))

    # Indexes for performance
    __table_args__ = (
        Index('idx_index_symbol_datetime', 'symbol', 'datetime'),
        Index('idx_index_datetime_desc', 'datetime', postgresql_using='btree'),
    )

    def __repr__(self):
        return f"<IndexOHLCV(symbol='{self.symbol}', datetime='{self.datetime}', close={self.close})>"


class FuturesOHLCV(Base):
    """Futures OHLCV data model optimized for TimescaleDB - matches reference stock_ohlcv structure with futures-specific fields."""

    __tablename__ = "futures_ohlcv"

    symbol = Column(Text, nullable=False, primary_key=True, index=True)
    exchange = Column(Text, nullable=False, default='NSE', primary_key=True)
    interval = Column('interval', Text, nullable=False, default='1m', primary_key=True)
    datetime = Column(DateTime(timezone=False), nullable=False, primary_key=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    expiry_date = Column(Date, nullable=False, primary_key=True)
    open_interest = Column(BigInteger, default=0)
    created_at = Column(DateTime(timezone=False), server_default=text("(now() AT TIME ZONE 'UTC')"))

    # Indexes for performance
    __table_args__ = (
        Index('idx_futures_symbol_datetime', 'symbol', 'datetime'),
        Index('idx_futures_expiry_datetime', 'expiry_date', 'datetime'),
        Index('idx_futures_datetime_desc', 'datetime', postgresql_using='btree'),
    )

    def __repr__(self):
        return f"<FuturesOHLCV(symbol='{self.symbol}', expiry='{self.expiry_date}', close={self.close})>"


class OptionsOHLCV(Base):
    """Options OHLCV data model optimized for TimescaleDB - matches reference stock_ohlcv structure with options-specific fields."""

    __tablename__ = "options_ohlcv"

    symbol = Column(Text, nullable=False, primary_key=True, index=True)
    exchange = Column(Text, nullable=False, default='NSE', primary_key=True)
    interval = Column('interval', Text, nullable=False, default='1m', primary_key=True)
    datetime = Column(DateTime(timezone=False), nullable=False, primary_key=True)
    open = Column(Float, nullable=False)
    high = Column(Float, nullable=False)
    low = Column(Float, nullable=False)
    close = Column(Float, nullable=False)
    volume = Column(BigInteger, nullable=False)
    expiry_date = Column(Date, nullable=False, primary_key=True)
    strike_price = Column(Float, nullable=False, primary_key=True)
    option_type = Column(Enum(OptionType), nullable=False, primary_key=True)
    open_interest = Column(BigInteger, default=0)
    created_at = Column(DateTime(timezone=False), server_default=text("(now() AT TIME ZONE 'UTC')"))

    # Indexes for performance
    __table_args__ = (
        Index('idx_options_symbol_datetime', 'symbol', 'datetime'),
        Index('idx_options_expiry_strike', 'expiry_date', 'strike_price', 'option_type'),
        Index('idx_options_datetime_desc', 'datetime', postgresql_using='btree'),
    )

    def __repr__(self):
        return f"<OptionsOHLCV(symbol='{self.symbol}', strike={self.strike_price}, type={self.option_type}, close={self.close})>"


class DataStatistics(Base):
    """Data statistics and metadata tracking for each symbol and market type."""

    __tablename__ = "data_statistics"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(100), nullable=False, index=True)
    market_type = Column(Enum(MarketType), nullable=False)
    exchange = Column(String(10), nullable=False, default="NSE")
    first_timestamp = Column(DateTime(timezone=True))
    last_timestamp = Column(DateTime(timezone=True))
    total_records = Column(BigInteger, default=0)
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Unique constraint
    __table_args__ = (
        Index('idx_symbol_market_type_stats', 'symbol', 'market_type', 'exchange', unique=True),
    )

    def __repr__(self):
        return f"<DataStatistics(symbol='{self.symbol}', market_type='{self.market_type}', records={self.total_records})>"


class DataResumption(Base):
    """Data resumption tracking for handling network failures and resuming data fetching."""

    __tablename__ = "data_resumption"

    id = Column(Integer, primary_key=True, index=True)
    symbol = Column(String(100), nullable=False, index=True)
    market_type = Column(Enum(MarketType), nullable=False)
    exchange = Column(String(10), nullable=False, default="NSE")
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    last_fetched_date = Column(DateTime(timezone=True))
    status = Column(String(20), nullable=False, default="PENDING")  # PENDING, IN_PROGRESS, COMPLETED, FAILED
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Indexes for performance
    __table_args__ = (
        Index('idx_resumption_symbol_status', 'symbol', 'status'),
        Index('idx_resumption_status_updated', 'status', 'updated_at'),
    )

    def __repr__(self):
        return f"<DataResumption(symbol='{self.symbol}', status='{self.status}', last_fetched='{self.last_fetched_date}')>"


class SymbolMapping(Base):
    """Symbol mapping between different formats (NSE, Fyers, etc.)."""

    __tablename__ = "symbol_mapping"

    id = Column(Integer, primary_key=True, index=True)
    nse_symbol = Column(String(100), nullable=False, index=True)
    fyers_symbol = Column(String(100), nullable=False, index=True)
    market_type = Column(Enum(MarketType), nullable=False)
    exchange = Column(String(10), nullable=False, default="NSE")
    expiry_date = Column(Date)  # For F&O contracts
    strike_price = Column(DECIMAL(12, 4))  # For options
    option_type = Column(Enum(OptionType))  # For options
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Unique constraints
    __table_args__ = (
        Index('idx_nse_symbol_mapping', 'nse_symbol', 'market_type', unique=True),
        Index('idx_fyers_symbol_mapping', 'fyers_symbol', unique=True),
    )

    def __repr__(self):
        return f"<SymbolMapping(nse='{self.nse_symbol}', fyers='{self.fyers_symbol}', type='{self.market_type}')>"